{"name": "defineEditor", "productName": "Visual Define-XML Editor", "version": "1.1.16-current", "license": "AGPL-3.0-only", "private": "true", "description": "Visual Define-XML Editor", "scripts": {"build": "concurrently \"npm run build-main\" \"npm run build-renderer\"", "build-dll": "cross-env NODE_ENV=development node --trace-warnings -r babel-register ./node_modules/webpack/bin/webpack --config webpack.config.renderer.dev.dll.js --colors", "build-main": "cross-env NODE_ENV=production node --trace-warnings -r babel-register ./node_modules/webpack/bin/webpack --config webpack.config.main.prod.js --colors", "build-renderer": "cross-env NODE_ENV=production node --trace-warnings -r babel-register ./node_modules/webpack/bin/webpack --config webpack.config.renderer.prod.js --colors", "dev": "cross-env START_HOT=1 node -r babel-register ./internals/scripts/CheckPortInUse.js && cross-env START_HOT=1 npm run start-renderer-dev", "electron-rebuild": "electron-rebuild --parallel --force --types prod,dev,optional --module-dir app", "flow": "flow", "flow-typed": "rimraf flow-typed/npm && flow-typed install --overwrite || true", "lint": "cross-env NODE_ENV=development eslint --cache --format=node_modules/eslint-formatter-pretty .", "lint-fix": "npm run --silent lint -- --fix; exit 0", "lint-styles": "stylelint app/*.css app/components/*.css app/css/*.css --syntax scss", "lint-styles-fix": "stylefmt -r app/*.css app/components/*.css app/css/*.css", "moveReleaseFiles": "node --trace-warnings -r babel-register ./internals/scripts/MoveFiles.js", "cleanReleaseFolder": "node --trace-warnings -r babel-register ./internals/scripts/CleanReleaseFolder.js", "package": "npm run build && electron-builder --publish never", "cleanSwp": "find . \\( -name '*.swp' -o -name '*.swo' \\) -delete", "package-debug-win": "DEBUG_PROD=true npm run build && electron-builder --win --publish never", "package-debug": "DEBUG_PROD=true npm run build && electron-builder --linux --publish never", "package-linux": "npm run build && electron-builder --linux && npm run moveReleaseFiles", "package-mac": "npm run build && electron-builder --mac && npm run moveReleaseFiles", "package-win": "npm run build && electron-builder --win --x64 && npm run moveReleaseFiles", "package-win32": "npm run build && electron-builder --win --ia32", "package-all": "npm run cleanReleaseFolder && npm run package-linux && npm run package-win", "postinstall": "node -r babel-register internals/scripts/CheckNativeDep.js && npm run flow-typed && npm run build-dll && electron-builder install-app-deps && node node_modules/fbjs-scripts/node/check-dev-engines.js package.json", "postlint-fix": "prettier --ignore-path .es<PERSON><PERSON><PERSON> --single-quote --write '**/*.js'", "precommit": "lint-staged", "prestart": "npm run build", "start": "cross-env NODE_ENV=production electron --enable-transparent-visuals ./app/", "start-main-dev": "cross-env HOT=1 NODE_ENV=development APPIMAGE=/tmp/ electron -r babel-register --inspect=5858 ./app/main.dev.js", "start-renderer-dev": "cross-env NODE_ENV=development node --trace-warnings -r babel-register ./node_modules/webpack-dev-server/bin/webpack-dev-server --hot --config webpack.config.renderer.dev.js", "test": "cross-env NODE_ENV=test BABEL_DISABLE_CACHE=1 node --trace-warnings -r babel-register ./internals/scripts/RunTests.js", "test-all": "npm run lint && npm run flow && npm run build && npm run test && npm run test-e2e", "test-e2e": "cross-env NODE_ENV=test BABEL_DISABLE_CACHE=1 node --trace-warnings -r babel-register ./internals/scripts/RunTests.js e2e", "test-watch": "npm test -- --watch"}, "browserslist": "electron 1.6", "lint-staged": {"*.js": ["cross-env NODE_ENV=development eslint --cache --format=node_modules/eslint-formatter-pretty", "git add"]}, "build": {"productName": "DefineEditor", "appId": "org.nogi.DefineEditor", "files": ["dist/", "static/", "node_modules/", "index.html", "findInPage.html", "main.prod.js", "main.prod.js.map", "package.json"], "extraResources": [{"from": "app/static/", "to": "static", "filter": ["**/*", "!**/devExtensions/**"]}], "dmg": {"contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}]}, "nsis": {"artifactName": "${productName}.Setup.${version}.${ext}"}, "appImage": {"artifactName": "${productName}.${version}.${ext}"}, "win": {"target": ["nsis", "portable"], "publish": ["github"]}, "mac": {"target": ["pkg", "mas"]}, "linux": {"target": ["deb", "AppImage"], "category": "Development", "publish": ["github"]}, "directories": {"buildResources": "resources", "output": "release"}}, "repository": {"type": "git", "url": "https://github.com/defineEditor/editor.git"}, "author": {"name": "<PERSON>", "email": "dmit<PERSON>.<EMAIL>", "url": ""}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": ""}], "bugs": {"url": "http://github.com/defineEditor/"}, "keywords": ["define-xml", "cdisc", "odm", "specifications"], "homepage": "http://defineeditor.com", "jest": {"moduleNameMapper": {"\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$": "<rootDir>/internals/mocks/fileMock.js", "\\.(css|less|sass|scss)$": "identity-obj-proxy"}, "moduleFileExtensions": ["js"], "moduleDirectories": ["node_modules", "app/node_modules"], "transform": {"^.+\\.js$": "babel-jest"}, "setupFiles": ["./internals/scripts/CheckBuiltsExist.js"]}, "devDependencies": {"babel-core": "^6.26.3", "babel-eslint": "^10.0.3", "babel-jest": "^23.0.0", "babel-loader": "^7.1.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-dev-expression": "^0.2.1", "babel-plugin-flow-runtime": "^0.17.0", "babel-plugin-syntax-async-generators": "^6.13.0", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-es2015-classes": "^6.24.1", "babel-preset-env": "^1.7.0", "babel-preset-react": "^6.24.1", "babel-preset-react-optimize": "^1.0.1", "babel-preset-stage-0": "^6.24.1", "babel-register": "^6.26.0", "chalk": "^2.4.1", "chance": "^1.1.6", "concurrently": "^3.5.1", "cross-env": "^5.2.0", "cross-spawn": "^6.0.5", "css-loader": "^0.28.11", "detect-port": "^1.2.3", "electron": "^13.0.0", "electron-builder": "^22.0.0", "electron-devtools-installer": "3.0.0", "electron-rebuild": "^2.0.0", "enzyme": "^3.3.0", "enzyme-adapter-react-16": "^1.1.1", "enzyme-to-json": "^3.3.4", "eslint": "^6.8.0", "eslint-config-airbnb": "^18.0.1", "eslint-config-prettier": "^4.0.0", "eslint-config-standard": "^12.0.0", "eslint-formatter-pretty": "^2.1.1", "eslint-import-resolver-webpack": "^0.11.0", "eslint-plugin-compat": "^3.3.0", "eslint-plugin-flowtype": "^3.4.2", "eslint-plugin-import": "^2.12.0", "eslint-plugin-jest": "^22.3.0", "eslint-plugin-jsx-a11y": "6.2.3", "eslint-plugin-node": "^10.0.0", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^2.3.0", "eslint-plugin-standard": "^4.0.0", "extract-text-webpack-plugin": "^4.0.0-beta.0", "fbjs-scripts": "^0.8.3", "file-loader": "^1.1.11", "flow-bin": "^0.72.0", "flow-runtime": "^0.17.0", "flow-typed": "^2.4.0", "husky": "^1.1.2", "identity-obj-proxy": "^3.0.0", "jest": "^23.0.0", "lint-staged": "^7.1.2", "live-plugin-manager": "^0.14.1", "minimist": "^1.2.0", "node-sass": "^4.9.0", "npm-logical-tree": "^1.2.1", "prettier": "^1.12.1", "react-test-renderer": "^16.4.0", "redux-logger": "^3.0.6", "rimraf": "^2.6.2", "sass-loader": "^7.0.1", "sinon": "^5.0.10", "spectron": "^3.8.0", "style-loader": "^0.21.0", "stylefmt": "^6.0.0", "stylelint": "^9.2.1", "stylelint-config-standard": "^18.2.0", "terser-webpack-plugin": "^2.3.5", "url-loader": "^1.0.1", "webpack": "^4.8.3", "webpack-bundle-analyzer": "^3.3.2", "webpack-cli": "^3.3.4", "webpack-dev-server": "^3.1.4", "webpack-merge": "^4.1.2"}, "dependencies": {"@hot-loader/react-dom": "^16.8.6", "@material-ui/core": "^4.6.1", "@material-ui/icons": "^4.5.1", "@material-ui/lab": "^4.0.0-alpha.32", "archiver": "^5.0.2", "babel-plugin-prismjs": "^1.0.2", "bootstrap": "^4.1.1", "cla-wrapper": "^0.3.0", "clone": "^2.1.2", "csvtojson": "^2.0.8", "devtron": "^1.4.0", "dompurify": "^2.0.0", "downshift": "^1.31.16", "draft-js": "^0.11.5", "draft-js-export-html": "^1.3.3", "electron-context-menu": "^3.0.0", "electron-debug": "^2.0.0", "electron-log": "^4.1.2", "electron-store": "^8.0.0", "electron-unhandled": "^1.1.0", "electron-updater": "^4.2.0", "fast-deep-equal": "^1.1.0", "fs-extra": "^9.0.0", "i18next": "^21.6.0", "i18next-browser-languagedetector": "^6.1.0", "idb": "^4.0.5", "jquery": "^3.3.1", "jszip": "^3.1.5", "prismjs": "^1.15.0", "prop-types": "^15.6.2", "react": "^16.5.2", "react-bootstrap-table": "DmitryMK/react-bootstrap-table", "react-dom": "^16.5.2", "react-hot-loader": "^4.3.11", "react-i18next": "^11.15.0", "react-icons": "^3.4.0", "react-markdown": "^4.0.6", "react-redux": "^7.0.0", "react-sortable-hoc": "^1.6.1", "redux": "^4.0.0", "redux-undo": "^1.0.0-beta9-9-7", "source-map-support": "^0.5.6", "throttle-debounce": "^2.0.1", "typeface-roboto": "^0.0.54", "typeface-roboto-mono": "^0.0.54", "xlsx-populate": "^1.19.1", "xml2js": "^0.4.19", "xmlbuilder": "^10.1.1", "xport-js": "^0.1.0"}, "devEngines": {"node": ">=10.x", "npm": ">=4.x", "yarn": ">=0.21.3"}}