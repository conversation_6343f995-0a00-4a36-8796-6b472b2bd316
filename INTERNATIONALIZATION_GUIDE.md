# Visual Define-XML Editor 中文国际化支持

## 📋 实施概述

本项目已成功添加了中文国际化支持，使用 `react-i18next` 库实现多语言功能。

## 🛠️ 已完成的功能

### 1. 国际化框架搭建
- ✅ 安装了 `i18next`、`react-i18next` 和 `i18next-browser-languagedetector` 依赖
- ✅ 创建了国际化配置文件 `app/i18n/index.js`
- ✅ 设置了语言检测和本地存储功能

### 2. 语言文件创建
- ✅ 英文翻译文件：`app/i18n/locales/en/translation.json`
- ✅ 中文翻译文件：`app/i18n/locales/zh/translation.json`
- ✅ 包含了菜单、设置、对话框、通用按钮等常用文本的翻译

### 3. 组件国际化改造
- ✅ 修改了设置页面 (`app/components/settings/general.js`)
- ✅ 修改了设置标签页 (`app/components/settings/tabs.js`)
- ✅ 修改了初始消息对话框 (`app/components/modal/modalInitialMessage.js`)
- ✅ 修改了主应用组件 (`app/core/app.js`)

### 4. 语言切换功能
- ✅ 在设置页面添加了语言选择器
- ✅ 支持英文和中文切换
- ✅ 语言设置会保存到本地存储

## 🎯 主要特性

### 语言支持
- **英文 (English)**: `en`
- **中文 (简体中文)**: `zh`

### 自动语言检测
- 优先使用用户在设置中选择的语言
- 如果没有设置，则使用浏览器语言
- 默认回退到英文

### 持久化存储
- 用户选择的语言会保存到 localStorage
- 应用重启后会自动加载上次选择的语言

## 📁 文件结构

```
app/
├── i18n/
│   ├── index.js                    # 国际化配置文件
│   └── locales/
│       ├── en/
│       │   └── translation.json    # 英文翻译
│       └── zh/
│           └── translation.json    # 中文翻译
├── components/
│   ├── settings/
│   │   ├── general.js             # 设置页面（已国际化）
│   │   └── tabs.js                # 设置标签页（已国际化）
│   └── modal/
│       └── modalInitialMessage.js # 初始消息对话框（已国际化）
├── core/
│   └── app.js                     # 主应用组件（已国际化）
├── constants/
│   └── initialValues.js           # 添加了语言默认值
└── index.js                       # 导入国际化配置
```

## 🔧 使用方法

### 在组件中使用翻译

```javascript
import { useTranslation } from 'react-i18next';

const MyComponent = () => {
    const { t } = useTranslation();
    
    return (
        <div>
            <h1>{t('settings.title')}</h1>
            <button>{t('common.save')}</button>
        </div>
    );
};
```

### 在类组件中使用翻译

```javascript
import { withTranslation } from 'react-i18next';

class MyComponent extends React.Component {
    render() {
        const { t } = this.props;
        return (
            <div>
                <h1>{t('settings.title')}</h1>
            </div>
        );
    }
}

export default withTranslation()(MyComponent);
```

### 带参数的翻译

```javascript
// 在翻译文件中
{
    "dialog": {
        "content": "您正在使用版本 {{version}}"
    }
}

// 在组件中使用
{t('dialog.content', { version: '1.1.16' })}
```

## 🚀 下一步扩展

### 1. 添加更多组件的国际化
- 主菜单组件
- 编辑器标签页
- 数据表格
- 错误消息
- 帮助文档

### 2. 添加更多语言支持
- 日文 (ja)
- 韩文 (ko)
- 法文 (fr)
- 德文 (de)

### 3. 优化功能
- 添加语言切换动画
- 支持 RTL 语言
- 添加语言包懒加载

## 📝 翻译文件示例

### 添加新的翻译键值

在 `app/i18n/locales/en/translation.json`:
```json
{
    "newSection": {
        "title": "New Section",
        "description": "This is a new section"
    }
}
```

在 `app/i18n/locales/zh/translation.json`:
```json
{
    "newSection": {
        "title": "新章节",
        "description": "这是一个新章节"
    }
}
```

## 🔍 测试方法

1. 启动应用程序
2. 进入设置页面
3. 在"常规"标签页中找到"语言"选择器
4. 切换语言并观察界面文本变化
5. 重启应用验证语言设置是否持久化

## 📞 技术支持

如需添加更多翻译或修改现有翻译，请：
1. 编辑对应的 JSON 翻译文件
2. 在组件中使用 `t()` 函数调用翻译键
3. 测试语言切换功能

---

**注意**: 本国际化实现基于 react-i18next 库，提供了完整的多语言支持框架。所有用户界面文本都可以通过修改翻译文件来本地化。
