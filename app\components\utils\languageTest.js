/***********************************************************************************
* This file is part of Visual Define-XML Editor. A program which allows to review  *
* and edit XML files created using the CDISC Define-XML standard.                  *
* Copyright (C) 2018 Dmitry Kolosov                                                *
*                                                                                  *
* Visual Define-XML Editor is free software: you can redistribute it and/or modify *
* it under the terms of version 3 of the GNU Affero General Public License         *
*                                                                                  *
* Visual Define-XML Editor is distributed in the hope that it will be useful,      *
* but WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY   *
* or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Affero General Public License   *
* version 3 (http://www.gnu.org/licenses/agpl-3.0.txt) for more details.           *
***********************************************************************************/

import React from 'react';
import { useTranslation } from 'react-i18next';
import { makeStyles } from '@material-ui/core/styles';
import Paper from '@material-ui/core/Paper';
import Typography from '@material-ui/core/Typography';
import Button from '@material-ui/core/Button';
import Grid from '@material-ui/core/Grid';

const useStyles = makeStyles((theme) => ({
    root: {
        padding: theme.spacing(3),
        margin: theme.spacing(2),
        maxWidth: 600,
    },
    section: {
        marginBottom: theme.spacing(2),
    },
    button: {
        margin: theme.spacing(1),
    },
}));

const LanguageTest = () => {
    const classes = useStyles();
    const { t, i18n } = useTranslation();

    const changeLanguage = (lng) => {
        i18n.changeLanguage(lng);
    };

    return (
        <Paper className={classes.root}>
            <Typography variant="h4" gutterBottom>
                {t('settings.title')} - 语言测试
            </Typography>
            
            <div className={classes.section}>
                <Typography variant="h6">
                    当前语言: {i18n.language}
                </Typography>
            </div>

            <div className={classes.section}>
                <Typography variant="h6">
                    {t('settings.generalSettings')}
                </Typography>
                <Typography>
                    {t('settings.userName')}: 测试用户
                </Typography>
                <Typography>
                    {t('settings.language')}: {t('settings.chinese')}
                </Typography>
            </div>

            <div className={classes.section}>
                <Typography variant="h6">
                    {t('common.ok')} / {t('common.cancel')} / {t('common.save')}
                </Typography>
            </div>

            <div className={classes.section}>
                <Typography variant="h6">
                    导航菜单测试:
                </Typography>
                <Typography>
                    {t('navigation.studies')} | {t('navigation.editor')} | {t('navigation.settings')}
                </Typography>
            </div>

            <Grid container spacing={2}>
                <Grid item>
                    <Button 
                        variant="contained" 
                        color="primary"
                        className={classes.button}
                        onClick={() => changeLanguage('en')}
                    >
                        Switch to English
                    </Button>
                </Grid>
                <Grid item>
                    <Button 
                        variant="contained" 
                        color="secondary"
                        className={classes.button}
                        onClick={() => changeLanguage('zh')}
                    >
                        切换到中文
                    </Button>
                </Grid>
            </Grid>

            <div className={classes.section}>
                <Typography variant="body2" color="textSecondary">
                    测试说明: 点击上方按钮切换语言，观察文本是否正确变化。
                </Typography>
            </div>
        </Paper>
    );
};

export default LanguageTest;
